"use client";

import { useState, useEffect, useCallback } from 'react';
import { Drawer } from 'vaul';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useTonConnectUI } from '@tonconnect/ui-react';
import { toast } from 'sonner';
import { getAppConfig, type AppConfig } from '@/api/app-config-api';
import { AlertTriangle, ArrowDown } from 'lucide-react';
import { useRootContext } from '@/root-context';
import { getUserById } from '@/api/auth-api';
import { httpsCallable } from 'firebase/functions';
import { firebaseFunctions } from '@/root-context';

interface WithdrawDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface WithdrawResponse {
  success: boolean;
  message: string;
  netAmount: number;
  feeAmount: number;
  transactionHash: string;
}

export function WithdrawDrawer({ open, onOpenChange }: WithdrawDrawerProps) {
  const [tonConnectUI] = useTonConnectUI();
  const { currentUser, setCurrentUser } = useRootContext();
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [appConfig, setAppConfig] = useState<AppConfig | null>(null);
  const [loading, setLoading] = useState(false);
  const [configLoading, setConfigLoading] = useState(true);

  // Load app config when drawer opens
  useEffect(() => {
    if (open) {
      loadAppConfig();
    }
  }, [open]);

  const loadAppConfig = async () => {
    try {
      setConfigLoading(true);
      const config = await getAppConfig();
      setAppConfig(config);
    } catch (error) {
      console.error('Failed to load app config:', error);
      toast.error('Failed to load configuration');
    } finally {
      setConfigLoading(false);
    }
  };

  const refetchUser = useCallback(async () => {
    try {
      if (currentUser?.id) {
        const updatedUser = await getUserById(currentUser.id);
        if (updatedUser) {
          setCurrentUser(updatedUser);
          toast.success('Balance updated successfully!');
        }
      }
    } catch (error) {
      console.error('Failed to refetch user:', error);
      toast.error('Failed to update balance');
    }
  }, [currentUser?.id, setCurrentUser]);

  const validateAmount = (amount: string): boolean => {
    if (!amount || !appConfig || !currentUser?.balance) return false;
    
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) return false;
    
    // Check minimum withdrawal (1 TON)
    if (numAmount < 1) return false;
    
    // Check if user has sufficient available balance
    const availableBalance = currentUser.balance.sum - currentUser.balance.locked;
    return numAmount <= availableBalance;
  };

  const isValidAmount = validateAmount(withdrawAmount);
  const availableBalance = currentUser?.balance ? currentUser.balance.sum - currentUser.balance.locked : 0;

  const handleWithdraw = async () => {
    if (!isValidAmount || !appConfig) {
      toast.error('Invalid withdrawal amount');
      return;
    }

    if (!tonConnectUI.account?.address) {
      toast.error('Please connect your wallet first');
      return;
    }

    if (!currentUser?.ton_wallet_address) {
      toast.error('No wallet address found in your profile');
      return;
    }

    try {
      setLoading(true);
      
      const amount = parseFloat(withdrawAmount);
      
      // Call withdrawFunds cloud function
      const withdrawFundsFunction = httpsCallable<
        { amount: number },
        WithdrawResponse
      >(firebaseFunctions, 'withdrawFunds');

      const result = await withdrawFundsFunction({ amount });
      
      console.log('Withdrawal result:', result.data);
      toast.success(result.data.message);

      // Reset form and close drawer
      setWithdrawAmount('');
      onOpenChange(false);

      // Refetch user data to update balance
      await refetchUser();
      
    } catch (error) {
      console.error('Withdrawal failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Withdrawal failed. Please try again.';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setWithdrawAmount('');
    onOpenChange(false);
  };

  const getWithdrawFee = () => {
    if (!appConfig || !withdrawAmount) return 0;
    const amount = parseFloat(withdrawAmount);
    if (isNaN(amount)) return 0;
    return appConfig.withdrawalFee;
  };

  const getNetAmount = () => {
    if (!withdrawAmount) return 0;
    const amount = parseFloat(withdrawAmount);
    if (isNaN(amount)) return 0;
    return Math.max(0, amount - getWithdrawFee());
  };

  return (
    <Drawer.Root open={open} onOpenChange={onOpenChange}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
        <Drawer.Content className="bg-white flex flex-col rounded-t-[10px] h-fit mt-24 max-h-[80vh] fixed bottom-0 left-0 right-0 z-50">
          <div className="p-4 bg-white rounded-t-[10px] flex-1">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-gray-300 mb-8" />
            
            <div className="max-w-md mx-auto">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <ArrowDown className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Withdraw Funds</h2>
                  <p className="text-sm text-gray-500">Withdraw TON to your connected wallet</p>
                </div>
              </div>

              {configLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-ton-main mx-auto"></div>
                  <p className="text-sm text-gray-500 mt-2">Loading configuration...</p>
                </div>
              ) : (
                <>
                  {/* Disclaimer */}
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
                      <div className="text-sm">
                        <p className="font-medium text-red-800 mb-1">Withdrawal Information</p>
                        <ul className="text-red-700 space-y-1">
                          <li>• Minimum withdrawal: <strong>1 TON</strong></li>
                          <li>• Withdrawal fee: <strong>{appConfig?.withdrawalFee || 0.1} TON</strong></li>
                          <li>• Available balance: <strong>{availableBalance.toFixed(2)} TON</strong></li>
                          <li>• Funds will be sent to your connected wallet</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {/* Amount Input */}
                    <div className="space-y-2">
                      <Label htmlFor="withdraw-amount" className="text-sm font-medium text-gray-700">
                        Withdrawal Amount (TON)
                      </Label>
                      <Input
                        id="withdraw-amount"
                        type="number"
                        step="0.01"
                        min="1"
                        max={availableBalance}
                        placeholder="Enter amount to withdraw"
                        value={withdrawAmount}
                        onChange={(e) => setWithdrawAmount(e.target.value)}
                        className="text-lg"
                        disabled={loading}
                      />
                      {withdrawAmount && (
                        <div className="text-xs text-gray-500 space-y-1">
                          <div className="flex justify-between">
                            <span>Withdrawal amount:</span>
                            <span>{parseFloat(withdrawAmount).toFixed(2)} TON</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Withdrawal fee:</span>
                            <span>-{getWithdrawFee().toFixed(2)} TON</span>
                          </div>
                          <div className="flex justify-between font-medium border-t pt-1">
                            <span>You will receive:</span>
                            <span>{getNetAmount().toFixed(2)} TON</span>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Validation Messages */}
                    {withdrawAmount && !isValidAmount && (
                      <div className="text-sm text-red-600">
                        {parseFloat(withdrawAmount) < 1 
                          ? 'Minimum withdrawal amount is 1 TON'
                          : parseFloat(withdrawAmount) > availableBalance
                          ? 'Insufficient available balance'
                          : 'Invalid amount'
                        }
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-3 pt-4">
                      <Button
                        variant="outline"
                        onClick={handleClose}
                        className="flex-1"
                        disabled={loading}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleWithdraw}
                        disabled={!isValidAmount || loading || !tonConnectUI.account?.address}
                        className="flex-1 bg-red-600 hover:bg-red-700"
                      >
                        {loading ? 'Processing...' : 'Withdraw'}
                      </Button>
                    </div>

                    {!tonConnectUI.account?.address && (
                      <p className="text-sm text-red-600 text-center">
                        Please connect your wallet to make a withdrawal
                      </p>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
