export const MESSAGES = {
  TELEGRAM_ID_ERROR: "❌ Unable to identify your Telegram ID. Please try again.",
  GENERIC_ERROR: "❌ Something went wrong. Please try again later.",
  
  WELCOME: "🛍️ Welcome to the Marketplace Bot! This bot helps you access our marketplace platform. Use the buttons below to get started or open the full marketplace using the menu button.",

  HELP: "🤖 Marketplace Bot Help - Basic Commands: /start - Start the bot and show main menu, /help - Show this help message. Available Buttons: 🛒 My Buy Orders - View and manage your buy orders, 💰 My Sell Orders - View and manage your sell orders, 🔗 Get Referral Link - Get your referral link. You can also use the menu button to open the full marketplace web app.",

  ORDERS: {
    FETCHING_BUY: "🔍 Fetching your buy orders...",
    FETCHING_SELL: "🔍 Fetching your sell orders...",
    NO_BUY_ORDERS: "📭 You don't have any buy orders yet. Create your first buy order using the marketplace web app!",
    NO_SELL_ORDERS: "📭 You don't have any sell orders yet. Create your first sell order using the marketplace web app!",
    BUY_ORDERS_TITLE: (count: number) => `🛒 Your Buy Orders (${count} total)`,
    SELL_ORDERS_TITLE: (count: number) => `💰 Your Sell Orders (${count} total)`,
    ORDERS_READY_FOR_COMPLETION: (count: number) => `🟠 Orders ready for completion: ${count}`,
    GIFTS_READY_FOR_DELIVERY: (count: number) => `🎁 Gifts ready for delivery: ${count}`,
    SHOWING_LIMITED_ORDERS: "📝 Showing first 10 orders. Use the web app to see all orders.",
    FETCH_ERROR_BUY: "❌ Failed to fetch your buy orders. Please try again later.",
    FETCH_ERROR_SELL: "❌ Failed to fetch your sell orders. Please try again later.",
  },

  REFERRAL: {
    TITLE: "🔗 Your Referral Link",
    DESCRIPTION: "Share this link with friends to earn referral rewards! When someone signs up using your link and makes a purchase, you'll receive a percentage of the marketplace fee.",
    LINK_PREFIX: "📎 Your referral link:",
    HOW_IT_WORKS: "💰 How it works: • Share your link with friends • They open the marketplace using your link • When they make their first purchase, you earn a referral fee • The fee is automatically added to your marketplace balance 🎯 Start earning by sharing your link now!",
  },

  BUSINESS_CONNECTION: {
    NO_PENDING_ORDER: "No pending order found",
    ORDER_NOT_FOUND: "❌ Order not found. Please try again.",
    INCORRECT_GIFT: "❌ You are sending an incorrect gift. Please send a gift from the correct collection for this order.",
    PROCESSING_GIFT: "🔄 Processing gift for relayer...",
    GIFT_SENT_SUCCESS: "✅ Gift sent to relayer! 🎁 The buyer will be notified that their gift is ready. 📱 They can use the bot to receive their gift.",
    GIFT_PROCESSING_ERROR: (orderId: string, error: string) => `❌ Failed to process gift for order #${orderId}. Error: ${error} Please try again or contact support.`,
    GIFT_PROCESSING_GENERIC_ERROR: (orderId: string) => `❌ An error occurred while processing gift for order #${orderId}. Please try again later or contact support.`,
    NO_ORDER_FOR_PROCESSING: "❌ No order found for gift processing.",
    ALREADY_SENT_GIFT: "🎁 You have already sent a gift for this order. Processing your gift transfer...",
    GIFT_TRANSFERRED_SUCCESS: "🎁 Gift successfully transferred back to you!",
    GIFT_TRANSFER_ERROR: "❌ Failed to transfer gift. Please contact support.",
    GIFT_TRANSFER_MISSING_INFO: "❌ Unable to process gift transfer. Missing required information.",
    GIFT_TRANSFER_GENERIC_ERROR: "❌ Error transferring gift. Please contact support.",
  },

  SUPPORT: {
    CONTACT_INFO: "📞 Contact Support - If you need help with your orders or have any questions, please contact our support team: 📧 Email: <EMAIL> 💬 Telegram: @marketplace_support We're here to help! 🤝",
  },
} as const;

export const BUTTON_TEXTS = {
  MY_BUY_ORDERS: "🛒 My Buy Orders",
  MY_SELL_ORDERS: "💰 My Sell Orders", 
  GET_REFERRAL_LINK: "🔗 Get Referral Link",
  OPEN_MARKETPLACE: "🌐 Open Marketplace",
  CONTACT_SUPPORT: "📞 Contact Support",
  BACK_TO_ORDERS: "📋 Back to Orders",
  VIEW_ALL_ORDERS: "📋 View All Orders",
  SHARE_LINK: "📱 Share Link",
  VIEW_MY_ORDERS: "📋 View My Orders",
  CANCEL: "❌ Cancel",
} as const;
