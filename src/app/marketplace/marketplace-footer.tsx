"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  TrendingUp,
  <PERSON>,
  Gift,
  Building,
  Camera,
  Activity,
} from "lucide-react";

export default function MarketplaceFooter() {
  const navItems = [
    {
      icon: TrendingUp,
      label: "Market",
      active: true,
    },
    {
      icon: Hammer,
      label: "Auctions",
      active: false,
    },
    {
      icon: Gift,
      label: "My Gifts",
      active: false,
    },
    {
      icon: Building,
      label: "GiFi",
      active: false,
    },
    {
      icon: Camera,
      label: "Gallery",
      active: false,
    },
    {
      icon: Activity,
      label: "Activity",
      active: false,
    },
  ];

  return (
    <footer className="bg-slate-800 text-white border-t border-slate-700">
      <div className="flex items-center justify-around py-3">
        {navItems.map((item, index) => {
          const IconComponent = item.icon;
          return (
            <Button
              key={index}
              variant="ghost"
              className={`flex flex-col items-center gap-1 p-2 h-auto ${
                item.active ? "text-blue-400" : "text-gray-400 hover:text-white"
              }`}
            >
              <IconComponent className="w-6 h-6" />
              <span className="text-xs font-medium">{item.label}</span>
            </Button>
          );
        })}
      </div>
    </footer>
  );
}
