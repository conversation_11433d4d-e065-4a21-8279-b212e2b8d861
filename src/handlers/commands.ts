import { Context } from "telegraf";
import {
  createMainKeyboard,
  createMarketplaceInlineKeyboard,
} from "../utils/keyboards";
import { MESSAGES } from "../constants/messages";

export const handleStartCommand = (ctx: Context) => {
  ctx.reply(MESSAGES.WELCOME, createMainKeyboard());
};

export const handleHelpCommand = (ctx: Context) => {
  ctx.reply(MESSAGES.HELP, createMarketplaceInlineKeyboard());
};
