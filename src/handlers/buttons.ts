import { Context, Markup } from "telegraf";
import {
  getUserOrdersByTgId,
  formatOrderForDisplay,
  getCompletableOrders,
  getGiftReadyOrders,
} from "../firebase-service";
import {
  createMarketplaceInlineKeyboard,
  createOrderHelpKeyboard,
  createEchoModeKeyboard,
  createReferralKeyboard,
  WEB_APP_URL,
} from "../utils/keyboards";
import { setUserSession } from "../services/session";

export const handleGetMyBuyOrdersButton = async (ctx: Context) => {
  try {
    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    ctx.reply("🔍 Fetching your buy orders...");

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.buyOrdersCount === 0) {
      ctx.reply(
        "📭 You don't have any buy orders yet.\n\n" +
          "Create your first buy order using the marketplace web app!",
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    const buyOrders = ordersResponse.buyOrders;
    const completableOrders = getCompletableOrders(buyOrders);
    const giftReadyOrders = getGiftReadyOrders(buyOrders);

    let message = `🛒 Your Buy Orders (${buyOrders.length} total)\n\n`;

    if (completableOrders.length > 0) {
      message += `🟠 Orders ready for completion: ${completableOrders.length}\n\n`;
    }

    if (giftReadyOrders.length > 0) {
      message += `🎁 Gifts ready for delivery: ${giftReadyOrders.length}\n\n`;
    }

    const orderButtons = buyOrders
      .slice(0, 10)
      .filter((order) => order.status === "paid")
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    orderButtons.push([
      Markup.button.callback("🌐 Open Marketplace", "open_marketplace"),
    ]);

    if (buyOrders.length > 10) {
      message += `\n📝 Showing first 10 orders. Use the web app to see all orders.`;
    }

    ctx.reply(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    console.error("Error fetching user buy orders:", error);
    ctx.reply(
      "❌ Failed to fetch your buy orders. Please try again later.",
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handleGetMySellOrdersButton = async (ctx: Context) => {
  try {
    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    ctx.reply("🔍 Fetching your sell orders...");

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.sellOrdersCount === 0) {
      ctx.reply(
        "📭 You don't have any sell orders yet.\n\n" +
          "Create your first sell order using the marketplace web app!",
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    const sellOrders = ordersResponse.sellOrders;
    const completableOrders = sellOrders.filter(
      (order) => order.status === "paid"
    );
    const giftReadyOrders = sellOrders.filter(
      (order) => order.status === "gift_sent_to_relayer"
    );

    let message = `💰 Your Sell Orders (${sellOrders.length} total)\n\n`;

    if (completableOrders.length > 0) {
      message += `🟠 Orders ready for completion: ${completableOrders.length}\n\n`;
    }

    if (giftReadyOrders.length > 0) {
      message += `🎁 Gifts ready for delivery: ${giftReadyOrders.length}\n\n`;
    }

    // Create inline keyboard with order buttons
    const orderButtons = sellOrders
      .slice(0, 10)
      .filter((order) => order.status === "paid")
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    // Add navigation buttons
    orderButtons.push([
      Markup.button.callback("🌐 Open Marketplace", "open_marketplace"),
    ]);

    if (sellOrders.length > 10) {
      message += `\n📝 Showing first 10 orders. Use the web app to see all orders.`;
    }

    ctx.reply(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    console.error("Error fetching user sell orders:", error);
    ctx.reply(
      "❌ Failed to fetch your sell orders. Please try again later.",
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handleCompleteOrderButton = (ctx: Context) => {
  ctx.reply(
    "📦 Complete Order\n\n" +
      "To complete an order, please use the marketplace web app where you can:\n" +
      "• View your active orders\n" +
      "• Track order status\n" +
      "• Complete payment\n" +
      "• Leave feedback\n\n" +
      "Click the button below to open the marketplace:",
    createOrderHelpKeyboard()
  );
};

export const handleEchoGiftButton = (ctx: Context) => {
  const userId = ctx.from?.id?.toString();
  if (!userId) {
    ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
    return;
  }

  // Set echo mode for this user
  setUserSession(userId, { echoMode: true });

  ctx.reply(
    "🎁 Echo Gift Mode Activated!\n\n" +
      "Send me any gift (photo, document, text, etc.) and I'll echo it back to you with logged data.\n\n" +
      "📝 The gift data will be logged to the console for debugging purposes.\n\n" +
      "To exit echo mode, use /start or click Cancel below.",
    createEchoModeKeyboard()
  );
};

export const handleReceiveGiftButton = async (ctx: Context) => {
  try {
    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    ctx.reply("🔍 Checking for gifts ready for delivery...");

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.orders.length === 0) {
      ctx.reply(
        "📭 No orders found.\n\n" +
          "Create your first order using the marketplace web app!",
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    const giftReadyOrders = getGiftReadyOrders(ordersResponse.orders);

    if (giftReadyOrders.length === 0) {
      ctx.reply(
        "🎁 No gifts ready for delivery yet.\n\n" +
          "When a seller sends a gift to the relayer for your order, you'll be notified and can receive it here!",
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    let message = `🎁 You have ${giftReadyOrders.length} gift(s) ready for delivery!\n\n`;

    // Create buttons for each gift-ready order
    const giftButtons = giftReadyOrders
      .slice(0, 10)
      .map((order) => [
        Markup.button.callback(
          `🎁 Receive Gift - Order #${order.number}`,
          `receive_gift_${order.id}`
        ),
      ]);

    // Add navigation buttons
    giftButtons.push([
      Markup.button.callback("📋 View All Orders", "back_to_orders"),
    ]);
    giftButtons.push([
      Markup.button.callback("🌐 Open Marketplace", "open_marketplace"),
    ]);

    if (giftReadyOrders.length > 10) {
      message += `\n📝 Showing first 10 gifts. Use the web app to see all orders.`;
    }

    ctx.reply(message, Markup.inlineKeyboard(giftButtons));
  } catch (error) {
    console.error("Error fetching gift-ready orders:", error);
    ctx.reply(
      "❌ Failed to check for gifts. Please try again later.",
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handleGetReferralLinkButton = (ctx: Context) => {
  const tgId = ctx.from?.id?.toString();
  if (!tgId) {
    ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
    return;
  }

  const referralLink = `${WEB_APP_URL}?referral_id=${tgId}`;

  ctx.reply(
    "🔗 Your Referral Link\n\n" +
      "Share this link with friends to earn referral rewards! When someone signs up using your link and makes a purchase, you'll receive a percentage of the marketplace fee.\n\n" +
      `📎 Your referral link:\n${referralLink}\n\n` +
      "💰 How it works:\n" +
      "• Share your link with friends\n" +
      "• They open the marketplace using your link\n" +
      "• When they make their first purchase, you earn a referral fee\n" +
      "• The fee is automatically added to your marketplace balance\n\n" +
      "🎯 Start earning by sharing your link now!",
    createReferralKeyboard(referralLink)
  );
};
