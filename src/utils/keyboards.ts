import { Markup } from "telegraf";
import dotenv from "dotenv";

dotenv.config();

const WEB_APP_URL =
  process.env.WEB_APP_URL ?? "https://4d5rqhd0-3000.euw.devtunnels.ms/";

export const createMainKeyboard = () => {
  return Markup.keyboard([
    [Markup.button.text("🎁 Echo Gift")],
    [
      Markup.button.text("🛒 Get My Buy Orders"),
      Markup.button.text("💰 Get My Sell Orders"),
    ],
    [
      Markup.button.text("🎁 Receive Gift"),
      Markup.button.text("✅ Complete Order"),
    ],
    [Markup.button.text("🔗 Get Referral Link")],
    [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
  ]).resize();
};

export const createMarketplaceInlineKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
  ]);
};

export const createOrderHelpKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
    [Markup.button.callback("📋 Order Help", "order_help")],
  ]);
};

export const createEchoModeKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.callback("❌ Cancel Echo Mode", "cancel_echo_mode")],
  ]);
};

export const createOrderActionsKeyboard = (orderId: string) => {
  return Markup.inlineKeyboard([
    [
      Markup.button.callback(
        "🎁 I'm ready to send gift",
        `complete_${orderId}`
      ),
    ],
    [Markup.button.callback("🔙 Back to Orders", "back_to_orders")],
    [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
  ]);
};

export const createOrderBackKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.callback("🔙 Back to Orders", "back_to_orders")],
    [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
  ]);
};

export const createSupportKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
    [Markup.button.callback("📞 Contact Support", "contact_support")],
  ]);
};

export const createReferralKeyboard = (referralLink: string) => {
  return Markup.inlineKeyboard([
    [
      Markup.button.url(
        "📱 Share Link",
        `https://t.me/share/url?url=${encodeURIComponent(
          referralLink
        )}&text=${encodeURIComponent(
          "🛍️ Join me on this awesome marketplace! Use my referral link to get started:"
        )}`
      ),
    ],
    [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
  ]);
};

export const createOrderCompletionKeyboard = (orderId: string) => {
  return Markup.inlineKeyboard([
    [Markup.button.callback("❌ Cancel", `order_${orderId}`)],
  ]);
};

export const createOrderSuccessKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.callback("📋 View My Orders", "back_to_orders")],
    [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
  ]);
};

export const createOrderErrorKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.callback("📋 View My Orders", "back_to_orders")],
    [Markup.button.callback("📞 Contact Support", "contact_support")],
  ]);
};

export { WEB_APP_URL };
