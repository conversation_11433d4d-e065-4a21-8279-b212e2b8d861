import { Context } from "telegraf";
import dotenv from "dotenv";
import { getUserSession, clearUserSession } from "../services/session";
import {
  sendGift<PERSON><PERSON><PERSON><PERSON><PERSON>,
  getOrderByIdByBot,
  getOrderByBuyerIdAndStatusByBot,
} from "../firebase-service";

dotenv.config();

interface OrderData {
  id: string;
  collectionId: string;
  buyerId?: string;
  sellerId?: string;
  status: string;
  [key: string]: any;
}

const BOT_TOKEN = process.env.BOT_TOKEN;

export type TelegramBusinessMessageContext = {
  update_id: number;
  business_message: {
    business_connection_id: string;
    message_id: number;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
      username: string;
      language_code: string;
      is_premium: boolean;
    };
    chat: {
      id: number;
      first_name: string;
      username: string;
      type: "private" | "group" | "supergroup" | "channel";
    };
    date: number;
    unique_gift: {
      gift: {
        owned_gift_id: string;
        gift: {
          id: string;
          sticker: any;
          star_count: number;
          total_count?: number;
          remaining_count?: number;
          upgrade_star_count?: number;
        };
        date: number;
        is_saved: boolean;
        is_name_hidden: boolean;
        is_unique?: boolean; // Unique gifts can be transferred
      }; // Replace with exact shape if known
      origin: string;
    };
  };
};

const getSentGiftId = (ctx: Context) => {
  const businessMessage = (ctx.update as any).business_message;
  return businessMessage?.unique_gift?.gift?.owned_gift_id;
};

const getGiftCollectionId = (ctx: Context) => {
  const businessMessage = (ctx.update as any).business_message;
  return businessMessage?.unique_gift?.gift?.gift?.id;
};

const getBusinessConnectionId = (ctx: Context) => {
  const businessMessage = (ctx.update as any).business_message;
  return businessMessage?.business_connection_id;
};

// Get order by ID using cloud function
const getOrderById = async (orderId: string): Promise<OrderData | null> => {
  try {
    const result = await getOrderByIdByBot(orderId);
    if (result.success && result.order) {
      return result.order as OrderData;
    }
    return null;
  } catch (error) {
    console.error("Error getting order:", error);
    return null;
  }
};

// Get order by buyerId and status using cloud function
const getOrderByBuyerIdAndStatus = async (
  buyerId: string,
  status: string
): Promise<OrderData | null> => {
  try {
    const result = await getOrderByBuyerIdAndStatusByBot(buyerId, status);
    if (result.success && result.order) {
      return result.order as OrderData;
    }
    return null;
  } catch (error) {
    console.error("Error getting order by buyer and status:", error);
    return null;
  }
};

// Transfer gift back to user
const transferGift = async (
  ctx: Context,
  businessConnectionId: string,
  chatId: number,
  ownedGiftId: string
) => {
  try {
    const sendGiftResponse = await fetch(
      `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          business_connection_id: businessConnectionId,
          new_owner_chat_id: chatId,
          star_count: 25,
          owned_gift_id: ownedGiftId,
        }),
      }
    );

    const sendGiftResult = (await sendGiftResponse.json()) as {
      ok: boolean;
      description?: string;
      error_code?: number;
    };

    console.log("sendGiftResult", sendGiftResult);

    if (sendGiftResult.ok) {
      console.log(`Successfully sent gift back to user ${chatId}`);
      ctx.reply("🎁 Gift successfully transferred back to you!");
    } else {
      console.error("Failed to transfer gift:", sendGiftResult);
      ctx.reply("❌ Failed to transfer gift. Please contact support.");
    }
  } catch (error) {
    console.error("Error transferring gift:", error);
    ctx.reply("❌ Error transferring gift. Please contact support.");
  }
};

export const businessConnectionMiddleware = async (
  ctx: Context,
  next: () => Promise<void>
) => {
  try {
    const update = ctx.update as unknown as TelegramBusinessMessageContext;
    if (!update?.business_message) {
      return;
    }

    const userId = update.business_message.from?.id?.toString();
    if (!userId) return;

    const session = getUserSession(userId);
    const pendingOrderId = session?.pendingOrderId;

    if (!pendingOrderId) {
      ctx.reply("No pending order found");
      return;
    }

    console.log("Pending ORDER ID", pendingOrderId);

    const giftIdToTransfer = getSentGiftId(ctx);

    if (giftIdToTransfer) {
      const giftCollectionId = getGiftCollectionId(ctx);

      // Get order with firestore by pendingOrderId, check if collectionId matches
      const order = await getOrderById(pendingOrderId);

      if (!order) {
        ctx.reply("❌ Order not found. Please try again.");
        return;
      }

      // Check if the gift collection ID matches the order's collection ID
      if (order.collectionId !== giftCollectionId) {
        ctx.reply(
          "❌ You are sending an incorrect gift. Please send a gift from the correct collection for this order."
        );
        return;
      }

      // Gift is correct, process it
      await handleGiftToRelayer(ctx, pendingOrderId, giftIdToTransfer);

      // Clear user session after successful processing
      clearUserSession(userId);
      return;
    }

    // If no gift to transfer, check if user has an order with status 'gift_sent_to_relayer'
    const existingOrder = await getOrderByBuyerIdAndStatus(
      userId,
      "gift_sent_to_relayer"
    );

    if (!existingOrder) {
      ctx.reply("❌ No order found for gift processing.");
      return;
    }

    // User has already sent gift for this order, transfer gift back to them
    ctx.reply(
      "🎁 You have already sent a gift for this order. Processing your gift transfer..."
    );

    const businessConnectionId = getBusinessConnectionId(ctx);

    if (businessConnectionId && giftIdToTransfer) {
      await transferGift(
        ctx,
        businessConnectionId,
        update.business_message.chat.id,
        giftIdToTransfer
      );
      clearUserSession(userId);
    } else {
      ctx.reply(
        "❌ Unable to process gift transfer. Missing required information."
      );
    }

    await next();
  } catch (error) {
    console.error("Error handling update:", error);
    await next();
  }
};

export const handleGiftToRelayer = async (
  ctx: Context,
  orderId: string,
  owned_gift_id: string
) => {
  try {
    ctx.reply("🔄 Processing gift for relayer...");

    // Update order status to gift_sent_to_relayer
    const result = await sendGiftToRelayer(orderId, owned_gift_id);

    if (result.success) {
      ctx.reply(
        `✅ Gift sent to relayer!\n\n` +
          "🎁 The buyer will be notified that their gift is ready.\n" +
          "📱 They can use the bot to receive their gift."
      );
    } else {
      ctx.reply(
        `❌ Failed to process gift for order #${orderId}.\n\n` +
          "Error: " +
          (result.message ?? "Unknown error") +
          "\n\n" +
          "Please try again or contact support."
      );
    }
  } catch (error) {
    console.error("Error handling gift to relayer:", error);
    ctx.reply(
      `❌ An error occurred while processing gift for order #${orderId}.\n\n` +
        "Please try again later or contact support."
    );
  }
};
