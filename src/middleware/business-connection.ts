import { Context } from "telegraf";
import dotenv from "dotenv";
import { getUserSession } from "../services/session";
import { sendGiftToRelayer } from "../firebase-service";

dotenv.config();

const BOT_TOKEN = process.env.BOT_TOKEN;

export type TelegramBusinessMessageContext = {
  update_id: number;
  business_message: {
    business_connection_id: string;
    message_id: number;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
      username: string;
      language_code: string;
      is_premium: boolean;
    };
    chat: {
      id: number;
      first_name: string;
      username: string;
      type: "private" | "group" | "supergroup" | "channel";
    };
    date: number;
    unique_gift: {
      gift: {
        owned_gift_id: string;
        gift: {
          id: string;
          sticker: any;
          star_count: number;
          total_count?: number;
          remaining_count?: number;
          upgrade_star_count?: number;
        };
        date: number;
        is_saved: boolean;
        is_name_hidden: boolean;
        is_unique?: boolean; // Unique gifts can be transferred
      }; // Replace with exact shape if known
      origin: string;
    };
  };
};

const getSentGiftId = (ctx: Context) => {
  const businessMessage = (ctx.update as any).business_message;
  // TODO save owned_gift_id in DB when status will be changed to gift_sent_to_relayer
  return businessMessage?.unique_gift?.owned_gift_id;
};

export const businessConnectionMiddleware = async (
  ctx: Context,
  next: () => Promise<void>
) => {
  try {
    const update = ctx.update as unknown as TelegramBusinessMessageContext;
    if (!update?.business_message) {
      return;
    }

    const userId = update.business_message.from?.id?.toString();
    if (!userId) return;

    const session = getUserSession(userId);
    const pendingOrderId = session?.pendingOrderId;

    if (!pendingOrderId) {
      ctx.reply("No pending order found");
      return;
    }

    console.log("Pending ORDER ID", pendingOrderId);

    const giftIdToTransfer = getSentGiftId(ctx);

    if (giftIdToTransfer) {
      // get order with firestore by pendingOrderId, check in order collectionId. If it is not equal to businessMessage?.unique_gift.gift.gift.id? then reply in context that you are sending incorrect gift and return
      // If the gift is correct, reply that gift is successfully sent to relayer and call handleGiftToRelayer then
      // after success clear user session

      handleGiftToRelayer(ctx, pendingOrderId, giftIdToTransfer);
    }

    // if no gift to transfer, get order from DB, where buyerId is userId, and order status is `gift_sent_to_relayer`, use direct firestore call for that
    // if no such order, reply to user, order by this ID not found, if order found, reply to user, you have already sent gift for this order, wait for relayer to process it
    // and call method transferGift, I commented next code which u need to use (and eventually clean up user session):
     // Now send a new gift to the sender using the converted stars
    // const sendGiftResponse = await fetch(
    //   `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
    //   {
    //     method: "POST",
    //     headers: {
    //       "Content-Type": "application/json",
    //     },
    //     body: JSON.stringify({
    //       business_connection_id: businessConnectionId,
    //       new_owner_chat_id: businessMessage.chat.id,
    //       star_count: 25,
    //       owned_gift_id: giftIdToTransfer as string,
    //     }),
    //   }
    // );


  // const sendGiftResult = (await sendGiftResponse.json()) as {
    //   ok: boolean;
    //   description?: string;
    //   error_code?: number;
    // };

    // console.log("sendGiftResult", sendGiftResult);

    // if (sendGiftResult.ok) {
    //   console.log(`Successfully sent gift back to user ${senderId}`);
    // }


    if (!giftIdToTransfer) {
      console.log("No gift ID found in business message");
      return;
    }

    // TODO call function

    await next();
  } catch (error) {
    console.error("Error handling update:", error);
    await next();
  }
};

export const handleGiftToRelayer = async (
  ctx: Context,
  orderId: string,
  owned_gift_id: string
) => {
  try {
    ctx.reply("🔄 Processing gift for relayer...");

    // Update order status to gift_sent_to_relayer
    const result = await sendGiftToRelayer(orderId!, owned_gift_id);

    if (result.success) {
      ctx.reply(
        `✅ Gift sent to relayer!\n\n` +
          "🎁 The buyer will be notified that their gift is ready.\n" +
          "📱 They can use the bot to receive their gift."
      );
    } else {
      ctx.reply(
        `❌ Failed to process gift for order #${orderId}.\n\n` +
          "Error: " +
          (result.message || "Unknown error") +
          "\n\n" +
          "Please try again or contact support."
      );
    }
  } catch (error) {
    console.error("Error handling gift to relayer:", error);
    ctx.reply(
      `❌ An error occurred while processing gift for order #${orderId}.\n\n` +
        "Please try again later or contact support."
    );
  }
};
