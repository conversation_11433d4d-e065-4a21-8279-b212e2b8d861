import { Collection } from "@/core.constants";
import { firestore } from "@/root-context";
import {
  collection,
  deleteDoc,
  doc,
  DocumentSnapshot,
  getDocs,
  limit,
  orderBy,
  query,
  setDoc,
  startAfter,
  updateDoc,
  writeBatch,
} from "firebase/firestore";

const COLLECTION_NAME = "collections";

export const createCollection = async (collectionData: Collection) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, collectionData.id);
    await setDoc(docRef, {
      ...collectionData,
    });
    return collectionData.id;
  } catch (error) {
    console.error("Error creating collection:", error);
    throw error;
  }
};

export const updateCollection = async (
  id: string,
  collectionData: Partial<Collection>
) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...collectionData,
    });
  } catch (error) {
    console.error("Error updating collection:", error);
    throw error;
  }
};

export const deleteCollection = async (id: string) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting collection:", error);
    throw error;
  }
};

export const getCollections = async (
  pageSize: number = 10,
  lastDoc?: DocumentSnapshot
) => {
  try {
    let q = query(
      collection(firestore, COLLECTION_NAME),
      orderBy("name", "asc"),
      limit(pageSize)
    );

    if (lastDoc) {
      q = query(
        collection(firestore, COLLECTION_NAME),
        orderBy("name", "asc"),
        startAfter(lastDoc),
        limit(pageSize)
      );
    }

    const snapshot = await getDocs(q);
    const collections: Collection[] = [];

    snapshot.forEach((doc) => {
      collections.push({ id: doc.id, ...doc.data() } as Collection);
    });

    return {
      collections,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === pageSize,
    };
  } catch (error) {
    console.error("Error fetching collections:", error);
    throw error;
  }
};

export const clearAllCollections = async () => {
  try {
    const snapshot = await getDocs(collection(firestore, COLLECTION_NAME));
    const batch = writeBatch(firestore);

    snapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Deleted ${snapshot.docs.length} collections`);
  } catch (error) {
    console.error("Error clearing collections:", error);
    throw error;
  }
};

export const createBulkCollections = async (
  collections: Omit<Collection, "id">[]
) => {
  try {
    const batch = writeBatch(firestore);
    const collectionsCollection = collection(firestore, COLLECTION_NAME);

    collections.forEach((collectionData) => {
      const docRef = doc(collectionsCollection);
      batch.set(docRef, {
        ...collectionData,
      });
    });

    await batch.commit();
    console.log(`Created ${collections.length} collections`);
  } catch (error) {
    console.error("Error creating bulk collections:", error);
    throw error;
  }
};
