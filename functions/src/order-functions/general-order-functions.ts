import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import {
  spendLockedFunds,
  unlockFunds,
  updateUserBalance,
} from "../balance-service";
import {
  DEFAULT_BUYER_LOCK_PERCENTAGE,
  DEFAULT_SELLER_LOCK_PERCENTAGE,
} from "../constants";
import { applyPurchaseFeeWithReferral, getAppConfig } from "../fee-service";
import { OrderEntity, OrderStatus } from "../types";

export const cancelOrder = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { orderId, userId } = data;

  if (!orderId || !userId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "orderId and userId are required."
    );
  }

  if (context.auth.uid !== userId) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "You can only cancel your own orders."
    );
  }

  try {
    const db = admin.firestore();

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check if user is authorized to cancel this order
    if (order.buyerId !== userId && order.sellerId !== userId) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You can only cancel orders where you are the buyer or seller."
      );
    }

    // Check if order can be cancelled
    if (order.status === OrderStatus.FULFILLED) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Cannot cancel a fulfilled order."
      );
    }

    if (order.status === OrderStatus.CANCELLED) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order is already cancelled."
      );
    }

    // Get app config for lock percentages
    const config = await getAppConfig();
    const buyerLockPercentage =
      config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE;
    const sellerLockPercentage =
      config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;

    // Calculate locked amounts
    const buyerLockedAmount = order.amount * buyerLockPercentage;
    const sellerLockedAmount = order.amount * sellerLockPercentage;

    // Unlock funds based on order status and participants
    const unlockPromises = [];

    if (order.buyerId) {
      unlockPromises.push(unlockFunds(order.buyerId, buyerLockedAmount));
    }

    if (order.sellerId) {
      unlockPromises.push(unlockFunds(order.sellerId, sellerLockedAmount));
    }

    await Promise.all(unlockPromises);

    // Update order status to cancelled
    await db.collection("orders").doc(orderId).update({
      status: OrderStatus.CANCELLED,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: "Order cancelled successfully. Locked funds have been released.",
      order: {
        id: order.id,
        number: order.number,
        status: OrderStatus.CANCELLED,
      },
    };
  } catch (error) {
    console.error("Error cancelling order:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while cancelling order."
    );
  }
});

export const completePurchase = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { orderId, userId } = data;

    if (!orderId || !userId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "orderId and userId are required."
      );
    }

    if (context.auth.uid !== userId) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You can only complete your own orders."
      );
    }

    try {
      const db = admin.firestore();

      // Get order
      const orderDoc = await db.collection("orders").doc(orderId).get();
      if (!orderDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Order not found.");
      }

      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Check if user is authorized to complete this order
      if (order.buyerId !== userId && order.sellerId !== userId) {
        throw new functions.https.HttpsError(
          "permission-denied",
          "You can only complete orders where you are the buyer or seller."
        );
      }

      // Check order status - must be gift_sent_to_relayer to complete
      if (order.status !== OrderStatus.GIFT_SENT_TO_RELAYER) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order must be in 'gift_sent_to_relayer' status to complete."
        );
      }

      // Check if order has both buyer and seller
      if (!order.buyerId || !order.sellerId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order must have both buyer and seller."
        );
      }

      // Get buyer's referral information and app config
      const [buyerDoc, config] = await Promise.all([
        db.collection("users").doc(order.buyerId).get(),
        getAppConfig(),
      ]);
      const buyerData = buyerDoc.data();
      const referralId = buyerData?.referral_id;
      const buyerLockPercentage =
        config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE;
      const sellerLockPercentage =
        config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;
      const buyerLockedAmount = order.amount * buyerLockPercentage;
      const sellerLockedAmount = order.amount * sellerLockPercentage;

      // Apply purchase fee with referral logic
      const feeResult = await applyPurchaseFeeWithReferral(
        order.buyerId,
        order.amount,
        referralId
      );

      const netAmountToSeller = order.amount - feeResult.totalFee;

      // Spend locked funds from both buyer and seller
      await Promise.all([
        spendLockedFunds(order.buyerId, buyerLockedAmount),
        spendLockedFunds(order.sellerId, sellerLockedAmount),
      ]);

      // Transfer net amount to seller
      await updateUserBalance(order.sellerId, netAmountToSeller);

      // Update order status to fulfilled
      await db.collection("orders").doc(orderId).update({
        status: OrderStatus.FULFILLED,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: "Purchase completed successfully.",
        netAmountToSeller,
        feeAmount: feeResult.totalFee,
        order: {
          id: order.id,
          number: order.number,
          status: OrderStatus.FULFILLED,
        },
      };
    } catch (error) {
      console.error("Error completing purchase:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while completing purchase."
      );
    }
  }
);
